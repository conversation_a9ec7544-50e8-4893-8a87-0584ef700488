<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class CleanupTempEditorImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'editor:cleanup-temp-images {--hours=24 : Hours after which temp images should be deleted}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up temporary editor images older than specified hours';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hours = $this->option('hours');
        $cutoffTime = Carbon::now()->subHours($hours);
        
        $this->info("Cleaning up temporary editor images older than {$hours} hours...");
        
        $disk = Storage::disk('public');
        $tempPath = 'editor/temp';
        
        if (!$disk->exists($tempPath)) {
            $this->info('No temporary editor images directory found.');
            return;
        }
        
        $files = $disk->files($tempPath);
        $deletedCount = 0;
        
        foreach ($files as $file) {
            $lastModified = Carbon::createFromTimestamp($disk->lastModified($file));
            
            if ($lastModified->lt($cutoffTime)) {
                $disk->delete($file);
                $deletedCount++;
                $this->line("Deleted: {$file}");
            }
        }
        
        $this->info("Cleanup completed. Deleted {$deletedCount} temporary images.");
    }
}
