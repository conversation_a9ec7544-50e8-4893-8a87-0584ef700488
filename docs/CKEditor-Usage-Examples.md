# CKEditor Component Usage Examples

## Cách sử dụng CKEditor Component với v-model

### 1. Import Component

```vue
<script setup>
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { ref } from 'vue';

const content = ref('');
</script>
```

### 2. Sử dụng cơ bản với v-model

```vue
<template>
  <div>
    <CkEditor v-model="content" />
  </div>
</template>

<script setup>
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { ref } from 'vue';

const content = ref('<p>Initial content here...</p>');
</script>
```

### 3. Với placeholder và disabled

```vue
<template>
  <div>
    <CkEditor 
      v-model="content" 
      placeholder="Enter your content here..."
      :disabled="isReadOnly"
    />
  </div>
</template>

<script setup>
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { ref } from 'vue';

const content = ref('');
const isReadOnly = ref(false);
</script>
```

### 4. Trong Form với validation

```vue
<template>
  <form @submit.prevent="handleSubmit">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Content *
      </label>
      <CkEditor 
        v-model="form.content" 
        placeholder="Enter article content..."
        :class="{ 'border-red-500': errors.content }"
      />
      <p v-if="errors.content" class="text-red-500 text-sm mt-1">
        {{ errors.content }}
      </p>
    </div>
    
    <button type="submit" class="btn btn-primary">
      Save Article
    </button>
  </form>
</template>

<script setup>
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { ref, reactive } from 'vue';
import { useForm } from '@inertiajs/vue3';

const form = useForm({
  title: '',
  content: '',
});

const errors = ref({});

const handleSubmit = () => {
  // Validate
  if (!form.content.trim()) {
    errors.value.content = 'Content is required';
    return;
  }
  
  // Submit form
  form.post('/admin/articles', {
    onSuccess: () => {
      // Handle success
    },
    onError: (formErrors) => {
      errors.value = formErrors;
    }
  });
};
</script>
```

### 5. Với Inertia Form

```vue
<template>
  <div>
    <form @submit.prevent="submit">
      <div class="mb-4">
        <label>Title</label>
        <input v-model="form.title" type="text" class="form-input" />
      </div>
      
      <div class="mb-4">
        <label>Content</label>
        <CkEditor v-model="form.content" />
      </div>
      
      <button type="submit" :disabled="form.processing">
        {{ form.processing ? 'Saving...' : 'Save' }}
      </button>
    </form>
  </div>
</template>

<script setup>
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { useForm } from '@inertiajs/vue3';

const props = defineProps({
  article: Object
});

const form = useForm({
  title: props.article?.title || '',
  content: props.article?.content || '',
});

const submit = () => {
  if (props.article) {
    // Update existing
    form.put(`/admin/articles/${props.article.id}`);
  } else {
    // Create new
    form.post('/admin/articles');
  }
};
</script>
```

### 6. Với watch để theo dõi thay đổi

```vue
<template>
  <div>
    <CkEditor v-model="content" />
    <p class="text-sm text-gray-500 mt-2">
      Characters: {{ characterCount }}
    </p>
  </div>
</template>

<script setup>
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { ref, watch, computed } from 'vue';

const content = ref('');

// Computed property để đếm ký tự
const characterCount = computed(() => {
  // Remove HTML tags để đếm text thuần
  const textOnly = content.value.replace(/<[^>]*>/g, '');
  return textOnly.length;
});

// Watch để auto-save
watch(content, (newContent) => {
  // Auto-save logic
  console.log('Content changed:', newContent);
  
  // Debounce auto-save
  clearTimeout(autoSaveTimeout);
  autoSaveTimeout = setTimeout(() => {
    autoSave(newContent);
  }, 2000);
});

let autoSaveTimeout;

const autoSave = (content) => {
  // Implement auto-save logic
  console.log('Auto-saving...', content);
};
</script>
```

### 7. Với multiple editors

```vue
<template>
  <div>
    <div class="mb-6">
      <label>Summary</label>
      <CkEditor 
        v-model="form.summary" 
        placeholder="Enter article summary..."
      />
    </div>
    
    <div class="mb-6">
      <label>Full Content</label>
      <CkEditor 
        v-model="form.content" 
        placeholder="Enter full article content..."
      />
    </div>
  </div>
</template>

<script setup>
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { reactive } from 'vue';

const form = reactive({
  summary: '',
  content: '',
});
</script>
```

## Props Available

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | String | `''` | Content của editor (dùng với v-model) |
| `placeholder` | String | `'Start typing...'` | Placeholder text |
| `disabled` | Boolean | `false` | Disable editor |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update:modelValue` | String | Emit khi content thay đổi |

## Lưu ý quan trọng

1. **v-model**: Component hỗ trợ v-model để binding 2-way
2. **HTML Content**: Content được lưu dưới dạng HTML
3. **Image Upload**: Ảnh được upload tạm thời, chỉ lưu permanent khi submit form
4. **Validation**: Có thể validate content như input thông thường
5. **Performance**: Editor chỉ khởi tạo khi component được mount
