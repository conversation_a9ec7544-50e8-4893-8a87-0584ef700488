# Ví dụ sử dụng CKEditor với Temporary Image Upload

## Ví dụ 1: Trong NotificationService

```php
<?php

namespace Src\Domain\Admin\Services;

use Src\Traits\EditorImageTrait;

class NotificationService
{
    use EditorImageTrait;

    public function store(NotificationForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                $attributes = $form->createAttributes();
                
                // Process editor content để move ảnh từ temp sang permanent
                if (isset($attributes['content'])) {
                    $attributes['content'] = $this->processEditorImages($attributes['content']);
                }
                
                /** @var Notification $notification */
                $notification = $this->notificationQuery()->createOrThrow($attributes);
                
                logger_info('Successfully stored Notification', ['id' => $notification->id]);
                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to store Notification: ', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    public function update(NotificationForm $form, int $id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $id) {
                /** @var Notification $notification */
                $notification = $this->notificationQuery()->findOrFail($id);
                
                $attributes = $form->editAttributes();
                
                // Process editor content
                if (isset($attributes['content'])) {
                    $attributes['content'] = $this->processEditorImages($attributes['content']);
                }
                
                $notification->updateOrThrow($attributes);
                
                logger_info('Successfully updated Notification', ['id' => $notification->id]);
                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to update Notification: ', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }
}
```

## Ví dụ 2: Trong FaqService với nhiều field editor

```php
<?php

namespace Src\Domain\Admin\Services;

use Src\Traits\EditorImageTrait;

class FaqService
{
    use EditorImageTrait;

    public function store(FaqForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                $data = $form->createAttributes();
                
                // Process multiple editor fields
                $editorFields = ['answer', 'description']; // Các field có chứa editor content
                $data = $this->processMultipleEditorFields($editorFields, $data);
                
                /** @var Faq $faq */
                $faq = $this->faqQuery()->createOrThrow($data);
                
                logger_info('Successfully stored FAQ', ['id' => $faq->id]);
                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to store FAQ: ', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }
}
```

## Ví dụ 3: Trong Controller

```php
<?php

namespace Src\Domain\Admin\Controllers;

use Src\Traits\EditorImageTrait;

class NotificationController extends Controller
{
    use EditorImageTrait;

    public function store(CreateRequest $request): RedirectResponse
    {
        $data = $request->validated();
        
        // Process editor content trước khi pass vào service
        if (isset($data['content'])) {
            $data['content'] = $this->processEditorImages($data['content']);
        }
        
        // Tạo form object với data đã được process
        $form = new NotificationForm($data);
        
        if ($this->service->store($form)) {
            return to_route('admin.notification.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }
}
```

## Ví dụ 4: Xử lý trong Form class

```php
<?php

namespace Src\Domain\Admin\Forms\Notification;

use Src\Domain\Admin\Forms\Form;
use Src\Traits\EditorImageTrait;

class NotificationForm extends Form
{
    use EditorImageTrait;

    public function createAttributes(): array
    {
        $attributes = [
            'title' => $this->get('title'),
            'content' => $this->get('content'),
            'is_public' => $this->get('is_public', false),
        ];

        // Process editor content
        if ($attributes['content']) {
            $attributes['content'] = $this->processEditorImages($attributes['content']);
        }

        return $attributes;
    }
}
```

## Testing

### Test upload temporary

```bash
# Test upload ảnh tạm thời
curl -X POST http://localhost/admin/editor/upload \
  -H "Content-Type: multipart/form-data" \
  -H "X-CSRF-TOKEN: your-csrf-token" \
  -F "upload=@test-image.jpg"
```

### Test cleanup command

```bash
# Test cleanup command
php artisan editor:cleanup-temp-images --hours=0

# Kiểm tra log
tail -f storage/logs/laravel.log
```

## Lưu ý quan trọng

1. **Luôn process content**: Đảm bảo gọi `processEditorImages()` hoặc `processMultipleEditorFields()` trước khi lưu vào database

2. **Xử lý trong transaction**: Đặt logic process trong DB transaction để đảm bảo consistency

3. **Error handling**: Nếu có lỗi, ảnh temp sẽ được cleanup tự động bởi scheduled command

4. **Performance**: Chỉ process khi thực sự có content editor để tránh overhead không cần thiết
