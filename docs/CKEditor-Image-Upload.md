# CKEditor Image Upload - Temporary Storage Implementation

## Tổng quan

Hệ thống upload ảnh cho CKEditor đã được cập nhật để sử dụng temporary storage. Ảnh chỉ được lưu vào storage chính khi form được submit thành công.

## Cách hoạt động

1. **Upload tạm thời**: Khi user chọn ảnh trong CKEditor, ảnh được upload vào thư mục `storage/editor/temp/`
2. **Hiển thị tạm thời**: Ảnh được hiển thị trong editor với URL tạm thời
3. **Chuyển đổi khi submit**: Khi form được submit, ảnh được move từ temp sang `storage/editor/images/` và tạo record trong database
4. **Cleanup**: Ảnh temp cũ được xóa tự động bằng command

## Cách sử dụng

### 1. Trong Service hoặc Controller

```php
<?php

namespace Src\Domain\Admin\Services;

use Src\Traits\EditorImageTrait;

class YourService
{
    use EditorImageTrait;

    public function store($form)
    {
        // Lấy content từ form
        $content = $form->getContent();
        
        // Process editor images (move từ temp sang permanent)
        $processedContent = $this->processEditorImages($content);
        
        // Lưu vào database với content đã được process
        $attributes = [
            'content' => $processedContent,
            // ... other fields
        ];
        
        return $this->model()->create($attributes);
    }
}
```

### 2. Xử lý nhiều field cùng lúc

```php
public function store($form)
{
    $data = $form->toArray();
    
    // Các field chứa editor content
    $editorFields = ['content', 'description', 'summary'];
    
    // Process tất cả editor fields
    $data = $this->processMultipleEditorFields($editorFields, $data);
    
    return $this->model()->create($data);
}
```

### 3. Trong Controller

```php
<?php

namespace Src\Domain\Admin\Controllers;

use Src\Traits\EditorImageTrait;

class YourController extends Controller
{
    use EditorImageTrait;

    public function store(YourRequest $request)
    {
        $data = $request->validated();
        
        // Process editor content
        if (isset($data['content'])) {
            $data['content'] = $this->processEditorImages($data['content']);
        }
        
        // Save to database
        $model = YourModel::create($data);
        
        return redirect()->route('your.index')->with('success', 'Created successfully');
    }
}
```

## Cleanup Command

Để xóa ảnh temporary cũ, chạy command:

```bash
# Xóa ảnh temp cũ hơn 24 giờ (mặc định)
php artisan editor:cleanup-temp-images

# Xóa ảnh temp cũ hơn 2 giờ
php artisan editor:cleanup-temp-images --hours=2
```

### Tự động cleanup

Thêm vào `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    // Chạy cleanup mỗi ngày lúc 2:00 AM
    $schedule->command('editor:cleanup-temp-images')->dailyAt('02:00');
}
```

## Cấu trúc thư mục

```
storage/app/public/
├── editor/
│   ├── temp/          # Ảnh tạm thời
│   └── images/        # Ảnh chính thức
```

## Lưu ý quan trọng

1. **Luôn process content trước khi lưu**: Đảm bảo gọi `processEditorImages()` trước khi lưu vào database
2. **Xử lý lỗi**: Nếu có lỗi trong quá trình submit, ảnh temp sẽ được cleanup tự động
3. **Performance**: Ảnh temp được cleanup định kỳ để tránh chiếm dung lượng
4. **URL structure**: 
   - Temp: `/storage/editor/temp/filename.jpg`
   - Permanent: `/storage/editor/images/filename.jpg`

## Troubleshooting

### Ảnh không hiển thị sau khi submit
- Kiểm tra xem có gọi `processEditorImages()` không
- Kiểm tra quyền ghi file trong thư mục storage
- Kiểm tra symlink `php artisan storage:link`

### Ảnh temp không được xóa
- Chạy manual cleanup: `php artisan editor:cleanup-temp-images`
- Kiểm tra cron job cho auto cleanup
- Kiểm tra quyền xóa file trong thư mục temp
