<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Language routes
Route::post('/language/change', 'LanguageController@change')->name('language.change');
Route::get('/language/current', 'LanguageController@current')->name('language.current');

Route::middleware(['guest:admin'])->group(function () {
    Route::get('/login', 'Login<PERSON>ontroller@show')->name('auth.show');
    Route::post('/login', '<PERSON><PERSON><PERSON><PERSON>roller@login')->name('auth.login');
});

Route::group(['middleware' => ['auth:admin']], function () {
    Route::post('/logout', 'LoginController@logout')->name('auth.logout');
    Route::get('/analytics', 'AnalyticController@index')->name('analytic.index');

    // User
    Route::get('/user', 'UserController@index')->name('user.index');
    Route::get('/user/create', 'UserController@create')->name('user.create');
    Route::get('/user/{user_id}/show', 'UserController@show')->name('user.show');
    Route::post('/user', 'UserController@store')->name('user.store');
    Route::delete('/user/{user_id}', 'UserController@delete')->name('user.delete');
    Route::patch('/user/{user_id}/update-base', 'UserController@updateBase')->name('user.update.base');
    Route::patch('/user/{user_id}/update-bank', 'UserController@updateBank')->name('user.update.bank');
    Route::get('/user/{user_id}/edit', 'UserController@edit')->name('user.edit');

    // Notification
    Route::get('/notification', 'NotificationController@index')->name('notification.index');
    Route::get('/notification/create', 'NotificationController@create')->name('notification.create');
    Route::get('/notification/{notification_id}/show', 'NotificationController@show')->name('notification.show');
    Route::post('/notification', 'NotificationController@store')->name('notification.store');
    Route::get('/notification/{notification_id}/edit', 'NotificationController@edit')->name('notification.edit');
    Route::put('/notification/{notification_id}', 'NotificationController@update')->name('notification.update');
    Route::delete('/notification/{notification_id}', 'NotificationController@delete')->name('notification.delete');

    // FAQ
    Route::get('/faq', 'FaqController@index')->name('faq.index');
    Route::get('/faq/create', 'FaqController@create')->name('faq.create');
    Route::get('/faq/{faq_id}/show', 'FaqController@show')->name('faq.show');
    Route::post('/faq', 'FaqController@store')->name('faq.store');
    Route::get('/faq/{faq_id}/edit', 'FaqController@edit')->name('faq.edit');
    Route::put('/faq/{faq_id}', 'FaqController@update')->name('faq.update');
    Route::delete('/faq/{faq_id}', 'FaqController@delete')->name('faq.delete');

    // Job
    Route::get('/job', 'JobController@index')->name('job.index');
    Route::get('/job/create', 'JobController@create')->name('job.create');
    Route::get('/job/{job_id}/show', 'JobController@show')->name('job.show');
    Route::post('/job', 'JobController@store')->name('job.store');
    Route::get('/job/{job_id}/edit', 'JobController@edit')->name('job.edit');
    Route::post('/job/{job_id}', 'JobController@update')->name('job.update');
    Route::delete('/job/{job_id}', 'JobController@delete')->name('job.delete');
    Route::post('/job/{job_id}/duplicate', 'JobController@duplicate')->name('job.duplicate');

    // Job Application
    Route::get('/job-application', 'JobApplicationController@index')->name('job-application.index');
    Route::get('/job-application/{job_id}/show', 'JobApplicationController@show')->name('job-application.show');
    Route::patch('/job-application/{job_id}/approval', 'JobApplicationController@approval')->name('job-application.approval');

    // Account
    Route::get('/account', 'AccountController@index')->name('account.index');
    Route::get('/account/create', 'AccountController@create')->name('account.create');
    // Route::get('/account/{account_id}/show', 'AccountController@show')->name('account.show');
    Route::post('/account', 'AccountController@store')->name('account.store');
    Route::get('/account/{account_id}/edit', 'AccountController@edit')->name('account.edit');
    Route::put('/account/{account_id}', 'AccountController@update')->name('account.update');
    Route::delete('/account/{account_id}', 'AccountController@delete')->name('account.delete');

    // Policy
    Route::get('/policy', 'PolicyController@index')->name('policy.index');
    Route::get('/policy/create', 'PolicyController@create')->name('policy.create');
    Route::get('/policy/{policy_id}/show', 'PolicyController@show')->name('policy.show');
    Route::post('/policy', 'PolicyController@store')->name('policy.store');
    Route::get('/policy/{policy_id}/edit', 'PolicyController@edit')->name('policy.edit');
    Route::put('/policy/{policy_id}', 'PolicyController@update')->name('policy.update');
    Route::delete('/policy/{policy_id}', 'PolicyController@delete')->name('policy.delete');

    // Maintenance
    Route::get('/maintenance', 'MaintenanceController@index')->name('maintenance.index');
    Route::get('/maintenance/create', 'MaintenanceController@create')->name('maintenance.create');
    Route::get('/maintenance/{maintenance_id}/show', 'MaintenanceController@show')->name('maintenance.show');
    Route::post('/maintenance', 'MaintenanceController@store')->name('maintenance.store');
    Route::get('/maintenance/{maintenance_id}/edit', 'MaintenanceController@edit')->name('maintenance.edit');
    Route::put('/maintenance/{maintenance_id}', 'MaintenanceController@update')->name('maintenance.update');
    Route::delete('/maintenance/{maintenance_id}', 'MaintenanceController@delete')->name('maintenance.delete');

    // Mail Template
    Route::get('/mail-template', 'MailTemplateController@index')->name('mail-template.index');
    Route::get('/mail-template/{mail_template_id}/show', 'MailTemplateController@show')->name('mail-template.show');
    Route::get('/mail-template/{mail_template_id}/edit', 'MailTemplateController@edit')->name('mail-template.edit');
    Route::put('/mail-template/{mail_template_id}', 'MailTemplateController@update')->name('mail-template.update');
});
