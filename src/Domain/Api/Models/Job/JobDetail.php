<?php

namespace Src\Domain\Api\Models\Job;

use App\Eloquent\Job;
use App\Eloquent\User;
use Illuminate\Support\Facades\Auth;
use Src\Domain\FormModel;
use Src\Enums\Gender;
use Illuminate\Contracts\Auth\Authenticatable;

/**
 * Class JobDetail
 * @package Src\Domain\Api\Models\Job
 */
class JobDetail extends FormModel
{
    /**
     * @var Job
     */
    protected Job $job;

    /**
     * JobDetail constructor.
     * @param Job $job
     */
    public function __construct(Job $job)
    {
        $this->job = $job;
    }

    /**
     * @return ?Authenticatable
     */
    public function getAuthor(): ?Authenticatable
    {
        return Auth::guard('api')->user();
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->job->id;
    }

    /**
     * @return string
     */
    public function getRecruitmentType(): string
    {
        return $this->job->recruitment_type;
    }

    /**
     * @return string
     */
    public function getThumbnailId(): string
    {
        return $this->job->thumbnail_id;
    }

    /**
     * @return string|null
     */
    public function getThumbnailUrl(): ?string
    {
        $path = optional($this->job->thumbnail)->file_path;
        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string
     */
    public function getEmployerEmail(): string
    {
        return $this->job->employer_email;
    }

    /**
     * @return string
     */
    public function getCategoryId(): string
    {
        return $this->job->category_id;
    }

    /**
     * @return string
     */
    public function getCategoryName(): string
    {
        return $this->job->category->name;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->job->type;
    }

    /**
     * @return boolean
     */
    public function getIsPublic(): bool
    {
        return $this->job->is_public;
    }

    /**
     * @return boolean
     */
    public function getIsInstant(): bool
    {
        return $this->job->is_instant;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->job->title;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->job->description;
    }

    /**
     * @return string| null
     */
    public function getBenefits(): string | null
    {
        return $this->job->benefits;
    }

    /**
     * @return string| null
     */
    public function getTimeStart(): string | null
    {
        return $this->job->time_start;
    }

    /**
     * @return string| null
     */
    public function getTimeEnd(): string | null
    {
        return $this->job->time_end;
    }

    /**
     * @return int| null
     */
    public function getAge(): int | null
    {
        return $this->job->age;
    }

    /**
     * @return string| null
     */
    public function getGender(): string | null
    {
        return $this->job->gender;
    }

    /**
     * @return string|null
     */
    public function getGenderName(): ?string
    {
        return Gender::getDescription($this->getGender());
    }

    /**
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->job->quantity;
    }

    /**
     * @return string
     */
    public function getCertificateLevel(): string
    {
        return $this->job->certificate_level;
    }

    /**
     * @return string
     */
    public function getPrefecture(): string
    {
        return $this->job->prefecture;
    }

    /**
     * @return string
     */
    public function getAddress(): string
    {
        return $this->job->address;
    }

    /**
     * @return string
     */
    public function getSalaryType(): string
    {
        return $this->job->salary_type;
    }

    /**
     * @return int
     */
    public function getSalary(): int
    {
        return $this->job->salary;
    }

    /**
     * @return string
     */
    public function getTravelFeeType(): string
    {
        return $this->job->travel_fee_type;
    }

    /**
     * @return string
     */
    public function getTravelFee(): string
    {
        return $this->job->travel_fee;
    }

    /**
     * @return string
     */
    public function getRecruitStartAt(): string
    {
        return $this->job->recruit_start_at;
    }

    /**
     * @return string
     */
    public function getRecruitExpiredAt(): string
    {
        return $this->job->recruit_expired_at;
    }

    /**
     * @return string
     */
    public function getJobStartAt(): string
    {
        return $this->job->job_start_at;
    }

    /**
     * @return bool
     */
    public function getIsFavorite(): bool
    {
        return $this->getAuthor()
            ? $this->job->favorites->contains($this->getAuthor()->id)
            : false;
    }

    /**
     * @return ?User
     */

    public function getUserJobApply(): ?User
    {
        $author = $this->getAuthor();

        if (!$author) {
            return null;
        }

        return $this->job->jobApply
            ->firstWhere(fn($apply) =>
                $apply->pivot->user_id === $this->getAuthor()->id &&
                $apply->pivot->job_id === $this->getId()
            );
    }

    /**
     * @return bool
     */
    public function getIsJobApply(): bool
    {
        return (bool)$this->getUserJobApply();
    }

    /**
     * @return string
     */
    public function getStatusJobApply(): string
    {
        return $this->getUserJobApply()?->pivot->approval_status ?? '';
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->job->created_at;
    }

    /**
     * @return array
     */
    public function toListApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'thumbnailUrl' => $this->getThumbnailUrl(),
            'isPublic' => $this->getIsPublic(),
            'isInstant' => $this->getIsInstant(),
            'title' => $this->getTitle(),
            'employerEmail' => $this->getEmployerEmail(),
            'quantity' => $this->getQuantity(),
            'salary' => $this->getSalary(),
            'salaryType' => $this->getSalaryType(),
            'prefecture' => $this->getPrefecture(),
            'isFavorite' => $this->getIsFavorite(),
            'isJobApply' => $this->getIsJobApply(),
            'statusJobApply' => $this->getStatusJobApply(),
            'createdAt' => $this->getCreatedAt()
        ];
    }

    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return array_merge($this->toListApiResponse(), [
            'recruitmentType' => $this->getRecruitmentType(),
            'categoryId' => $this->getCategoryId(),
            'categoryName' => $this->getCategoryName(),
            'type' => $this->getType(),
            'description' => $this->getDescription(),
            'gender' => $this->getGender(),
            'genderName' => $this->getGenderName(),
            'quantity' => $this->getQuantity(),
            'certificateLevel' => $this->getCertificateLevel(),
            'recruitStartAt' => $this->getRecruitStartAt(),
            'recruitExpiredAt' => $this->getRecruitExpiredAt(),
            'benefits' => $this->getBenefits(),
            'timeStart' => $this->getTimeStart(),
            'timeEnd' => $this->getTimeEnd(),
            'age' => $this->getAge(),
            'address' => $this->getAddress(),
            'salaryType' => $this->getSalaryType(),
            'salary' => $this->getSalary(),
            'travelFeeType' => $this->getTravelFeeType(),
            'travelFee' => $this->getTravelFee(),
            'isFavorite' => $this->getIsFavorite(),
            'isJobApply' => $this->getIsJobApply(),
            'statusJobApply' => $this->getStatusJobApply(),
        ]);
    }
}
